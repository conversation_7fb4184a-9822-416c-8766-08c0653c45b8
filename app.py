from kivy.app import App
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.core.window import Window
from kivy.core.text import LabelBase
from kivy.resources import resource_add_path
from kivy.graphics import Rectangle, Color
from kivy.core.image import Image as CoreImage
from kivy.clock import Clock
import os
import platform
import time
import datetime
import subprocess
import threading
if platform.system() == 'Windows':
    import win32gui
    import win32con


# ---------- 字体配置 ----------
# 添加字体资源路径 - 使用系统字体路径
# Windows 7 兼容性处理
if platform.system() == 'Windows':
    try:
        fonts_path = os.path.join(os.environ['WINDIR'], 'Fonts')
        resource_add_path(fonts_path)

        # 检查字体文件是否存在
        msyh_path = os.path.join(fonts_path, 'msyh.ttc')
        simhei_path = os.path.join(fonts_path, 'simhei.ttf')
        simsun_path = os.path.join(fonts_path, 'simsun.ttc')

        if os.path.exists(msyh_path):
            primary_font = msyh_path
        elif os.path.exists(simhei_path):
            primary_font = simhei_path
        elif os.path.exists(simsun_path):
            primary_font = simsun_path
        else:
            # 找不到中文字体时的备用方案
            primary_font = None
            print("警告: 找不到中文字体文件")

        # 注册中文字体
        if primary_font:
            LabelBase.register('chinese_font', primary_font)
    except Exception as e:
        print(f"字体配置错误: {e}")
        # 出错时不注册字体，应用将使用默认字体


class MainWidget(FloatLayout):
    def __init__(self, **kwargs):
        super(MainWidget, self).__init__(**kwargs)

        # 设置背景图片
        with self.canvas.before:
            # 加载背景图片
            try:
                bg_image = CoreImage('picture/3000背景.bmp')
                self.bg_rect = Rectangle(texture=bg_image.texture, pos=self.pos, size=self.size)
                self.bind(size=self._update_bg, pos=self._update_bg)
            except Exception as e:
                print(f"背景图片加载失败: {e}")
                # 使用纯色背景作为备用
                Color(0.2, 0.2, 0.8, 1)  # 蓝色背景
                self.bg_rect = Rectangle(pos=self.pos, size=self.size)
                self.bind(size=self._update_bg, pos=self._update_bg)

        # 创建综合业务按钮 - 使用您提供的按钮图片
        self.btn1 = Button(
            text='',  # 不显示文字，因为图片已包含文字
            background_normal='picture/p1.png',
            background_down='picture/p1.png',
            border=(0, 0, 0, 0),
            size_hint=(None, None),
            pos_hint={'center_x': 0.19, 'center_y': 0.80}  # 调整位置以匹配目标效果
        )

        # 创建个人业务按钮 - 使用您提供的按钮图片
        self.btn2 = Button(
            text='',  # 不显示文字，因为图片已包含文字
            background_normal='picture/p2.png',
            background_down='picture/p2.png',
            border=(0, 0, 0, 0),
            size_hint=(None, None),
            pos_hint={'center_x': 0.19, 'center_y': 0.64}  # 调整位置以匹配目标效果
        )

        # 设置按钮尺寸（根据图片实际尺寸或设置合适的尺寸）
        try:
            img1 = CoreImage('picture/p1.png')
            self.btn1.size = (img1.texture.width, img1.texture.height)

            img2 = CoreImage('picture/p2.png')
            self.btn2.size = (img2.texture.width, img2.texture.height)
        except Exception as e:
            print(f"按钮图片加载失败: {e}")
            # 使用合适的默认尺寸，匹配您提供的按钮图片样式
            self.btn1.size = (300, 80)  # 调整为更合适的尺寸
            self.btn2.size = (300, 80)

        # 绑定按钮事件
        self.btn1.bind(on_press=self.on_btn1_press)
        self.btn2.bind(on_press=self.on_btn2_press)

        # 添加按钮到布局
        self.add_widget(self.btn1)
        self.add_widget(self.btn2)

        # 创建时间显示标签
        # 检查是否有中文字体可用
        try:
            font_name = 'chinese_font'
        except:
            font_name = 'Roboto'

        self.time_label = Label(
            text=self.get_current_time(),
            font_name=font_name,
            font_size='16sp',
            color=(0, 0, 0, 1),  # 白色文字
            size_hint=(None, None),
            size=(200, 30),
            pos_hint={'right': 0.98, 'top': 0.98}  # 右上角位置
        )
        self.add_widget(self.time_label)

        # 启动时间更新定时器，每秒更新一次
        Clock.schedule_interval(self.update_time, 1)

    def get_current_time(self):
        """获取当前时间，格式：2025年07月16日 17:05:03"""
        now = datetime.datetime.now()
        return now.strftime("%Y年%m月%d日 %H:%M:%S")

    def update_time(self, dt):
        """更新时间显示"""
        self.time_label.text = self.get_current_time()

    def _update_bg(self, instance, value):
        """更新背景图片尺寸和位置"""
        if hasattr(self, 'bg_rect'):
            self.bg_rect.pos = instance.pos
            self.bg_rect.size = instance.size

    def on_btn1_press(self, instance):
        """综合业务按钮点击事件"""
        print("综合业务按钮被点击")
        # 在这里添加综合业务的处理逻辑
        # 可以添加跳转到综合业务界面的代码

    def on_btn2_press(self, instance):
        """个人业务按钮点击事件"""
        print("个人业务按钮被点击")
        # 在这里添加个人业务的处理逻辑
        # 可以添加跳转到个人业务界面的代码


class QueueApp(App):
    def __init__(self, **kwargs):
        super(QueueApp, self).__init__(**kwargs)
        self.external_process = None

    def build(self):
        # 设置窗口标题
        self.title = '智能排队管理系统'

        # 设置窗口为全屏无边框 - Windows 7 兼容性优化
        try:
            # 获取屏幕尺寸
            from kivy.core.window import Window

            # 设置全屏模式
            Window.fullscreen = 'auto'  # 自适应全屏
            Window.borderless = True    # 无边框

            # Windows 7 兼容性设置
            Window.always_on_top = False  # 不总是置顶
            Window.resizable = False      # 不可调整大小

            # 设置窗口背景色（备用）
            Window.clearcolor = (0.2, 0.2, 0.8, 1)  # 蓝色背景

            # 绑定窗口显示事件
            Window.bind(on_show=self.on_window_show)

        except Exception as e:
            print(f"窗口属性设置错误: {e}")

        return MainWidget()

    def on_window_show(self, window):
        """当窗口显示时的回调函数"""
        if platform.system() == 'Windows':
            # 延迟执行窗口管理，确保窗口完全显示
            threading.Timer(0.5, self.manage_windows).start()

    def manage_windows(self):
        """管理窗口显示：隐藏外部程序，显示当前窗口"""
        if platform.system() == 'Windows':
            try:
                # 隐藏外部程序窗口（如果存在）
                self.hide_external_program()

                # 确保当前窗口显示在前台
                self.bring_to_front()

            except Exception as e:
                print(f"窗口管理错误: {e}")

    def hide_external_program(self):
        """隐藏外部程序窗口"""
        try:
            # 查找并隐藏getnob.exe窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)

                    # 根据窗口标题或类名识别外部程序
                    if 'getnob' in window_text.lower() or 'getnob' in class_name.lower():
                        windows.append(hwnd)
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 隐藏找到的窗口
            for hwnd in windows:
                win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                print(f"隐藏外部程序窗口: {win32gui.GetWindowText(hwnd)}")

        except Exception as e:
            print(f"隐藏外部程序失败: {e}")

    def bring_to_front(self):
        """将当前窗口置于前台"""
        try:
            # 查找当前Kivy应用窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)

                    # 根据窗口标题识别当前应用
                    if '智能排队管理系统' in window_text:
                        windows.append(hwnd)
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 将窗口置于前台
            for hwnd in windows:
                win32gui.SetForegroundWindow(hwnd)
                win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                print(f"将窗口置于前台: {win32gui.GetWindowText(hwnd)}")

        except Exception as e:
            print(f"窗口置前失败: {e}")

    def start_external_program(self):
        """启动外部程序"""
        try:
            # 启动外部程序
            external_exe = "D:/Microsoft VS Code/Code.exe"
            if os.path.exists(external_exe):
                self.external_process = subprocess.Popen(external_exe)
                print(f"启动外部程序: {external_exe}")
                return True
            else:
                print(f"外部程序不存在: {external_exe}")
                return False
        except Exception as e:
            print(f"启动外部程序失败: {e}")
            return False


if __name__ == '__main__':

    # 启动Kivy应用
    try:
        app = QueueApp()
        app.run()
    except Exception as e:
        print(f"应用程序运行错误: {e}")
        input("按回车键退出...")  # 方便调试时查看错误信息